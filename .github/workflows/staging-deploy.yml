name: Deploy to Staging

on:
  push:
    branches: [ staging ]
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - name: Deploy to Staging Server
      run: |
        echo "Starting Staging Deployment..."
        
        # Create SSH directory and set permissions
        mkdir -p ~/.ssh
        chmod 700 ~/.ssh
        
        # Decode and save the EC2 private key
        echo "${{ secrets.STAGING_SSH_PRIVATE_KEY }}" | base64 -d > ~/.ssh/ec2-key
        chmod 600 ~/.ssh/ec2-key
        
        # Add staging EC2 to known hosts
        ssh-keyscan -H ec2-54-84-11-94.compute-1.amazonaws.com >> ~/.ssh/known_hosts
        
        # SSH into the server and deploy
        ssh -o StrictHostKeyChecking=no -i ~/.ssh/ec2-key <EMAIL> "bash -s" <<EOF
        set -e

        echo "Changing to project directory"
        cd /var/www/successionplanai

        echo "🔧 Enabling maintenance mode"
        php artisan down --message="Deployment in progress" --retry=60

        echo "Setting remote origin"
        git remote set-url origin https://${{ secrets.GH_PAT }}@github.com/SuccessionplanAI/successionplan-ai.git

        echo "Fixing permissions before git operations"
        sudo chown -R ubuntu:www-data /var/www/successionplanai/storage
        sudo chmod -R 775 /var/www/successionplanai/storage
        sudo chmod -R 775 /var/www/successionplanai/bootstrap/cache

        echo "Fetching and resetting to remote branch"
        git fetch origin
        git reset --hard origin/staging
        # Clean but exclude the virtual environment directory
        git clean -fd -e venv/

        echo "Fixing permissions after git operations"
        sudo chown -R ubuntu:www-data /var/www/successionplanai/storage
        sudo chmod -R 775 /var/www/successionplanai/storage

        echo "Creating and setting permissions for shared directory"
        sudo mkdir -p /var/www/successionplanai/shared
        sudo chown -R ubuntu:www-data /var/www/successionplanai/shared
        sudo chmod -R 775 /var/www/successionplanai/shared

        echo "🧹 Clearing application caches"
        php artisan cache:clear
        php artisan config:clear
        php artisan route:clear
        php artisan view:clear

        echo "🔄 Terminating Horizon workers gracefully"
        php artisan horizon:terminate || echo "Horizon not running or already terminated"

        echo "📦 Installing Composer dependencies"
        composer install --no-interaction --prefer-dist --no-dev --optimize-autoloader

        echo "📦 Installing NPM packages"
        npm install --legacy-peer-deps

        echo "🗄️ Running database migrations"
        php artisan migrate --force

        echo "🏗️ Building frontend assets"
        npm run build

        echo "⚡ Optimizing application for production"
        php artisan config:cache
        php artisan route:cache
        php artisan view:cache

        echo "🔄 Restarting queue workers"
        php artisan queue:restart

        echo "Final permission check for shared directories"
        ls -la /var/www/successionplanai/shared/
        sudo chown -R www-data:www-data /var/www/successionplanai/shared
        sudo chmod -R 777 /var/www/successionplanai/shared
        echo "Shared directory permissions updated to ensure PHP and Python can both access"

        echo "✅ Disabling maintenance mode"
        php artisan up

        echo "🚀 Deployment completed successfully!"
        EOF
